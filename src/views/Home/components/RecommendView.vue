<template>
  <!-- 推荐 -->
  <div class="recommend">
    <RecommendSection
      v-for="(section, index) in sections"
      :key="index"
      :items="section"
      @img-click="handleImgClick"
    />
  </div>
</template>

<script setup>
import { computed, toRefs } from 'vue'
import RecommendSection from '@views/Home/components/RecommendSection.vue'

const props = defineProps({
  hotGoods: {
    type: Array,
    default: () => []
  }
})

const { hotGoods } = toRefs(props)

// 将数据分组为4个区块
const sections = computed(() => {
  const result = []
  for (let i = 0; i < hotGoods.value.length; i += 2) {
    const section = hotGoods.value.slice(i, i + 2)
    if (section.length > 0) {
      result.push(section)
    }
  }
  return result
})

const handleImgClick = (url) => {
  if (url) {
    window.location.href = url
  }
}
</script>

<style lang="less" scoped>
.recommend {
  margin-top: 10px;
  padding: 0 @padding-page;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 10px;
}
</style>
